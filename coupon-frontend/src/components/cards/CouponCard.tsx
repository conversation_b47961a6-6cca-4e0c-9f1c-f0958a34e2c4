import React, { memo } from 'react';
import Button from '@/components/ui/Button';
import { Coupon } from '@/types/api';
import { isExpired } from '@/lib/utils';
import { useThemeClasses } from '@/config/theme';

interface CouponCardProps {
  coupon: Coupon;
  onClick: () => void;
  className?: string;
}

const CouponCard: React.FC<CouponCardProps> = ({ coupon, onClick, className }) => {
  const brand = coupon.brand;
  const theme = useThemeClasses();

  const getDisplayCode = (code: string | null | undefined): string => {
    if (!code) return 'NO CODE';

    // 显示一半的代码，另一半用***代替
    const halfLength = Math.ceil(code.length / 2);
    const visiblePart = code.substring(0, halfLength);
    return visiblePart + '***';
  };

  return (
    <div
      className={`ghibli-card group overflow-hidden cursor-pointer relative transform transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary-200/50 hover:-translate-y-1 ${className}`}
      onClick={() => onClick && onClick()}
    >
      {/* 过期警告标签 - 左上角 */}
      {isExpired(coupon.end_date) && (
        <div className={`absolute top-2 left-2 bg-error-100 text-error-700 px-2 py-1 rounded-full text-xs font-semibold z-10`}>
          Expired
        </div>
      )}

      {/* 移动端垂直布局，桌面端水平布局 */}
      <div className="flex flex-col mobile:flex-col md:flex-row items-center mobile:items-stretch p-mobile-4 md:p-5 pt-mobile-6 md:pt-7 relative h-full gap-mobile-4 md:gap-6 min-h-[180px] mobile:min-h-[200px] md:min-h-[160px]">
        {/* Decorative background pattern */}
        <div className={`absolute inset-0 bg-gradient-to-r from-transparent via-primary-50 to-secondary-50 opacity-50 group-hover:opacity-70 transition-all duration-300`}></div>

        {/* 品牌Logo - 移动端居中，桌面端左侧 */}
        <div className="flex-shrink-0 relative z-10 mobile:self-center md:self-auto transform group-hover:scale-110 transition-transform duration-300">
          {brand?.logo ? (
            <div className={`w-16 h-16 mobile:w-14 mobile:h-14 md:w-20 md:h-20 rounded-lg bg-white border border-neutral-200 flex items-center justify-center p-2 shadow-sm group-hover:shadow-lg hover:border-primary-300 transition-all duration-300`}>
              <img
                src={brand.logo}
                alt={brand.name}
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  // 如果图片加载失败，显示品牌名称首字母
                  const target = e.target as HTMLImageElement;
                  const parent = target.parentElement;
                  if (parent) {
                    parent.innerHTML = `
                      <div class="w-full h-full bg-gradient-to-br from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-xl mobile:text-lg md:text-2xl">${brand?.name?.charAt(0) || 'C'}</span>
                      </div>
                    `;
                  }
                }}
              />
            </div>
          ) : (
            <div className="w-16 h-16 mobile:w-14 mobile:h-14 md:w-20 md:h-20 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl mobile:text-lg md:text-2xl">
                {brand?.name?.charAt(0) || 'C'}
              </span>
            </div>
          )}
        </div>

        {/* 主要内容 - 移动端垂直布局，桌面端水平布局 */}
        <div className="flex-1 relative z-10 flex flex-col mobile:flex-col md:flex-row mobile:items-center md:items-center md:justify-between w-full">
          {/* 内容信息 - 移动端垂直排列，桌面端水平排列 */}
          <div className="flex flex-col mobile:items-center md:items-start mobile:gap-mobile-3 md:gap-2 mobile:w-full">
            {/* 内容信息 */}
            <div className="flex flex-col mobile:text-center md:text-left mobile:w-full">
              {/* 品牌名称 */}
              {brand && (
                <div className={`text-mobile-sm md:text-sm font-semibold ${theme.couponCard.brandName.text} mb-1 brand-name`} data-from-api>
                  {brand.name}
                </div>
              )}

              {/* 优惠券名称 */}
              <h3 className={`text-mobile-lg md:text-lg font-bold ${theme.couponCard.couponName.text} transition-colors line-clamp-2 mobile:line-clamp-2 md:line-clamp-1 mb-mobile-2 md:mb-2 coupon-title`} data-from-api>
                {coupon.name}
              </h3>

              {/* 描述信息 */}
              {coupon.description && (
                <p className="text-mobile-sm md:text-sm text-gray-600 line-clamp-2 mobile:line-clamp-2 md:line-clamp-1 deal-description" data-from-api>
                  {coupon.description}
                </p>
              )}
            </div>
          </div>

          {/* 代码、折扣和按钮 - 移动端垂直排列，桌面端水平排列 */}
          <div className="flex flex-col mobile:flex-col md:flex-row mobile:items-center md:items-center mobile:gap-mobile-3 md:gap-4 mobile:w-full mobile:mt-mobile-4 md:mt-0">
            {/* 代码显示 */}
            <div className={`font-mono font-bold ${theme.couponCard.code.text} text-mobile-base md:text-lg tracking-wider mobile:mb-mobile-2 md:mb-0 group-hover:scale-105 transition-all duration-300`}>
              {getDisplayCode(coupon.code)}
            </div>

            {/* 折扣信息 - 现在位于代码和按钮之间 */}
            {coupon.discount && (
              <div className={`w-16 h-10 mobile:w-20 mobile:h-12 md:w-20 md:h-12 flex items-center justify-center rounded-lg shadow-sm group-hover:shadow-lg group-hover:scale-105 transition-all duration-300`}
                   style={theme.couponCard.discountBadge.style}>
                <span className="text-sm mobile:text-base md:text-lg font-bold text-center leading-tight discount-amount" data-from-api>
                  {coupon.discount}
                </span>
              </div>
            )}

            {/* Get Code按钮 */}
            <Button
              className="px-mobile-4 md:px-6 py-mobile-3 md:py-2 rounded-lg font-semibold transition-colors whitespace-nowrap pointer-events-none min-h-touch mobile:w-full md:w-auto active:scale-95 transform transition-transform"
              style={theme.couponCard.button.style}
              disabled={isExpired(coupon.end_date)}
            >
              {isExpired(coupon.end_date) ? 'Expired' : 'Get Code'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(CouponCard);
