import React from 'react';
import { Link } from 'react-router-dom';
import { Brand } from '@/types/api';
import { useThemeClasses } from '@/config/theme';

interface BrandCardProps {
  brand: Brand;
  className?: string;
  onClick?: (brand: Brand) => void;
}

const BrandCard: React.FC<BrandCardProps> = ({ brand, className, onClick }) => {
  const theme = useThemeClasses();

  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick(brand);
    }
  };

  // 找到第一个有折扣信息的coupon
  const firstValidCoupon = brand.coupons?.find(coupon => coupon.discount && coupon.discount.trim() !== '') || null;

  const content = (
    <div className="relative h-full">
      {/* Brand Name - 浮动在卡片顶部边界 */}
      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-20 group/tooltip">
        <div className="bg-white border-2 border-green-500 rounded-full px-3 py-1 shadow-lg group-hover:border-green-600 group-hover:shadow-xl group-hover:bg-green-50 transition-all duration-300">
          <h3 
            className="text-xs font-bold text-gray-800 group-hover:text-green-600 transition-colors duration-300 whitespace-nowrap cursor-help"
            title={brand.name}
          >
            {brand.name.length > 12 ? `${brand.name.substring(0, 12)}...` : brand.name}
          </h3>
        </div>
        {/* Custom Tooltip */}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-lg whitespace-nowrap opacity-0 invisible group-hover/tooltip:opacity-100 group-hover/tooltip:visible transition-all duration-200 z-50 pointer-events-none">
          {brand.name}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
        </div>
      </div>

      {/* 卡片主体内容 */}
      <div className="p-mobile-2 md:p-3 text-center relative h-full flex flex-col pt-4">
        {/* Decorative background pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-blue-50 to-green-50 opacity-50 group-hover:from-green-50 group-hover:via-green-100 group-hover:to-blue-100 group-hover:opacity-70 transition-all duration-300"></div>

        {/* Brand Logo - 放大logo以突出显示 */}
        <div className="relative z-10 flex justify-center mb-3">
          <div className="w-20 h-20 mobile:w-18 mobile:h-18 md:w-22 md:h-22 group-hover:scale-110 transition-transform duration-300">
            {brand.logo ? (
              <img
                src={brand.logo}
                alt={brand.name}
                className="w-full h-full object-contain rounded-lg"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-base mobile:text-sm md:text-lg">🏪</span>
              </div>
            )}
          </div>
        </div>

        {/* 弹性空间，将折扣信息和Stats推到底部 */}
        <div className="flex-1"></div>

        {/* Coupon Info - 折扣信息 */}
        {firstValidCoupon && (
          <div className="relative z-10 mb-2">
            <div
              className="inline-flex items-center text-mobile-xs md:text-xs px-2 py-1 rounded-full shadow-md transform hover:scale-105 transition-transform duration-200"
              style={{
                background: theme.brandCard.discountBadge.background || 'linear-gradient(to right, #dc2626, #b91c1c)',
                color: theme.brandCard.discountBadge.text || '#ffffff'
              }}
            >
              <svg className="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.806.317L8.5 15.134l-2.661 2.439a1 1 0 01-1.806-.317L2.854 12.8-.5 10.866a1 1 0 010-1.732L2.854 7.2l1.179-4.456A1 1 0 015 2h7z" clipRule="evenodd" />
              </svg>
              {firstValidCoupon.discount}
            </div>
          </div>
        )}

        {/* Stats - 统计信息合并为一行 */}
        <div className="relative z-10 flex justify-center items-center">
          <div className="flex items-center space-x-1 text-xs">
            {brand.total_coupons > 0 && (
              <>
                <span className="font-bold text-blue-500">{brand.total_coupons}</span>
                <span className="text-gray-500">Coupons</span>
              </>
            )}
            {brand.total_coupons > 0 && brand.total_deals > 0 && (
              <span className="text-gray-400 mx-1">•</span>
            )}
            {brand.total_deals > 0 && (
              <>
                <span className="font-bold text-red-500">{brand.total_deals}</span>
                <span className="text-gray-500">Deals</span>
              </>
            )}
            {brand.total_coupons === 0 && brand.total_deals === 0 && (
              <>
                <span className="font-bold text-gray-400">0</span>
                <span className="text-gray-500">Offers</span>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  if (onClick) {
    return (
      <div
        className={`ghibli-card cursor-pointer group h-[160px] mobile:h-[140px] md:h-[180px] overflow-visible active:scale-95 transform transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-green-200/50 hover:-translate-y-1 ${className || ''}`}
        onClick={handleClick}
      >
        {content}
      </div>
    );
  }

  return (
    <Link
      to={`/brands/${brand.unique_name}`}
      className={`ghibli-card cursor-pointer group h-[160px] mobile:h-[140px] md:h-[180px] overflow-visible active:scale-95 transform transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-green-200/50 hover:-translate-y-1 ${className || ''}`}
    >
      {content}
    </Link>
  );
};

export default BrandCard;
