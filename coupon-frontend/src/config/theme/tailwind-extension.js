/**
 * Tailwind CSS主题扩展
 * 自动生成，请勿手动修改
 */

module.exports = {
  theme: {
    extend: {
      colors: {
      "primary": {
            "50": "#f0fdf4",
            "100": "#dcfce7",
            "200": "#bbf7d0",
            "300": "#86efac",
            "400": "#4ade80",
            "500": "#22c55e",
            "600": "#16a34a",
            "700": "#15803d",
            "800": "#166534",
            "900": "#14532d",
            "950": "#052e16"
      },
      "secondary": {
            "50": "#eff6ff",
            "100": "#dbeafe",
            "200": "#bfdbfe",
            "300": "#93c5fd",
            "400": "#60a5fa",
            "500": "#3b82f6",
            "600": "#2563eb",
            "700": "#1d4ed8",
            "800": "#1e40af",
            "900": "#1e3a8a",
            "950": "#172554"
      },
      "accent": {
            "50": "#faf5ff",
            "100": "#f3e8ff",
            "200": "#e9d5ff",
            "300": "#d8b4fe",
            "400": "#c084fc",
            "500": "#a855f7",
            "600": "#9333ea",
            "700": "#7c3aed",
            "800": "#6b21a8",
            "900": "#581c87",
            "950": "#3b0764"
      },
      "success": {
            "50": "#f0fdf4",
            "100": "#dcfce7",
            "200": "#bbf7d0",
            "300": "#86efac",
            "400": "#4ade80",
            "500": "#22c55e",
            "600": "#16a34a",
            "700": "#15803d",
            "800": "#166534",
            "900": "#14532d",
            "950": "#052e16"
      },
      "warning": {
            "50": "#fefce8",
            "100": "#fef9c3",
            "200": "#fef08a",
            "300": "#fde047",
            "400": "#facc15",
            "500": "#eab308",
            "600": "#ca8a04",
            "700": "#a16207",
            "800": "#854d0e",
            "900": "#713f12",
            "950": "#422006"
      },
      "error": {
            "50": "#fef2f2",
            "100": "#fee2e2",
            "200": "#fecaca",
            "300": "#fca5a5",
            "400": "#f87171",
            "500": "#ef4444",
            "600": "#dc2626",
            "700": "#b91c1c",
            "800": "#991b1b",
            "900": "#7f1d1d",
            "950": "#450a0a"
      },
      "info": {
            "50": "#eff6ff",
            "100": "#dbeafe",
            "200": "#bfdbfe",
            "300": "#93c5fd",
            "400": "#60a5fa",
            "500": "#3b82f6",
            "600": "#2563eb",
            "700": "#1d4ed8",
            "800": "#1e40af",
            "900": "#1e3a8a",
            "950": "#172554"
      },
      "neutral": {
            "50": "#fafafa",
            "100": "#f5f5f5",
            "200": "#e5e5e5",
            "300": "#d4d4d4",
            "400": "#a3a3a3",
            "500": "#737373",
            "600": "#525252",
            "700": "#404040",
            "800": "#262626",
            "900": "#171717",
            "950": "#0a0a0a"
      }
},
      
      spacing: {
      "xs": "0.5rem",
      "sm": "0.75rem",
      "md": "1rem",
      "lg": "1.5rem",
      "xl": "2rem"
},
      
      fontSize: {
      "xs": "0.75rem",
      "sm": "0.875rem",
      "base": "1rem",
      "lg": "1.125rem",
      "xl": "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
      "4xl": "2.25rem"
},
      
      borderRadius: {
      "sm": "0.25rem",
      "md": "0.375rem",
      "lg": "0.5rem",
      "xl": "0.75rem",
      "full": "9999px"
},
      
      backgroundImage: {
        'hero-gradient': 'linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)',
        'card-gradient': 'linear-gradient(to bottom right, transparent, #f9fafb, #f3f4f6)',
        'button-gradient': 'linear-gradient(to right, #16a34a, #15803d)',
        'discount-gradient': 'linear-gradient(to right, #dc2626, #b91c1c)',
        'hover-primary': 'linear-gradient(to bottom right, #dcfce7, #bbf7d0, #86efac)',
        'hover-secondary': 'linear-gradient(to bottom right, #dbeafe, #bfdbfe, #93c5fd)',
        'hover-accent': 'linear-gradient(to bottom right, #f3e8ff, #e9d5ff, #d8b4fe)',
      },
      
      boxShadow: {
        'theme-hover': '0 25px 50px -12px rgba(0, 0, 0, 0.15)',
        'theme-focus': '0 10px 25px -3px rgba(0, 0, 0, 0.1)',
      }
    }
  }
};