import { ThemeConfig } from './types';

/**
 * 默认主题配置
 * 保持当前网站的所有配色方案
 */
export const defaultTheme: ThemeConfig = {
  name: 'CouponsGo Default',
  description: 'Default theme for CouponsGo website',
  version: '1.0.0',

  colors: {
    // 主色调 - 绿色系（当前网站主色）
    primary: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16'
    },
    
    // 辅助色 - 蓝色系
    secondary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554'
    },
    
    // 强调色 - 紫色系
    accent: {
      50: '#faf5ff',
      100: '#f3e8ff',
      200: '#e9d5ff',
      300: '#d8b4fe',
      400: '#c084fc',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7c3aed',
      800: '#6b21a8',
      900: '#581c87',
      950: '#3b0764'
    },
    
    // 成功色 - 绿色系（与primary相同）
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16'
    },
    
    // 警告色 - 黄色系
    warning: {
      50: '#fefce8',
      100: '#fef9c3',
      200: '#fef08a',
      300: '#fde047',
      400: '#facc15',
      500: '#eab308',
      600: '#ca8a04',
      700: '#a16207',
      800: '#854d0e',
      900: '#713f12',
      950: '#422006'
    },
    
    // 错误色 - 红色系
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a'
    },
    
    // 信息色 - 蓝色系（与secondary相同）
    info: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554'
    },
    
    // 中性色 - 灰色系
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
      950: '#0a0a0a'
    }
  },

  components: {
    brandCard: {
      container: {
        background: "#ffffff",
        border: "#e5e7eb",
        shadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
        hover: {
          background: "#fafafa",
          border: "#16a34a",
          shadow: "0 4px 16px rgba(34, 197, 94, 0.2)"
        }
      },
      brandName: {
        text: "#16a34a",
        border: "#dcfce7",
        background: "rgba(220, 252, 231, 0.5)"
      },
      discountBadge: {
        background: "linear-gradient(to right, #dc2626, #b91c1c)",
        text: "#ffffff",
        border: "transparent"
      },
      couponCount: {
        text: "#16a34a",
        background: "rgba(34, 197, 94, 0.1)"
      },
      description: {
        text: "#6b7280"
      }
    },

    couponCard: {
      container: {
        background: "#ffffff",
        border: "#e5e7eb",
        shadow: "0 2px 8px rgba(0, 0, 0, 0.08)",
        hover: {
          background: "#ffffff",
          border: "#16a34a",
          shadow: "0 8px 25px rgba(34, 197, 94, 0.2)"
        }
      },
      discountBadge: {
        background: "linear-gradient(to right, #dc2626, #b91c1c)",
        text: "#ffffff",
        border: "transparent"
      },
      button: {
        background: "#16a34a",
        text: "#ffffff",
        border: "transparent",
        hover: "#15803d",
        disabled: {
          background: "#d1d5db",
          text: "#9ca3af"
        }
      },
      code: {
        text: "#15803d",
        background: "rgba(21, 128, 61, 0.1)",
        border: "#dcfce7"
      },
      brandName: {
        text: "#6b7280",
        hover: "#16a34a"
      },
      couponName: {
        text: "#111827"
      },
      description: {
        text: "#9ca3af"
      },
      expiry: {
        text: "#ca8a04",
        background: "rgba(202, 138, 4, 0.1)"
      }
    },

    categoryCard: {
      container: {
        background: "#ffffff",
        border: "#e5e7eb",
        shadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
        hover: {
          background: "#fafafa",
          border: "#16a34a",
          shadow: "0 4px 16px rgba(34, 197, 94, 0.15)"
        }
      },
      icon: {
        color: "#16a34a",
        background: "rgba(34, 197, 94, 0.1)"
      },
      text: {
        color: "#111827",
        hover: "#16a34a"
      },
      count: {
        text: "#6b7280",
        background: "rgba(107, 114, 128, 0.1)"
      }
    },

    dealCard: {
      container: {
        background: "#ffffff",
        border: "#e5e7eb",
        shadow: "0 2px 8px rgba(0, 0, 0, 0.08)",
        hover: {
          background: "#ffffff",
          border: "#2563eb",
          shadow: "0 8px 25px rgba(37, 99, 235, 0.15)"
        }
      },
      discountBadge: {
        background: "linear-gradient(to right, #dc2626, #b91c1c)",
        text: "#ffffff",
        border: "transparent"
      },
      button: {
        background: "#15803d",
        text: "#ffffff",
        border: "transparent",
        hover: "#15803d",
        disabled: {
          background: "#d1d5db",
          text: "#9ca3af"
        }
      },
      brandName: {
        text: "#6b7280",
        hover: "#2563eb"
      },
      dealName: {
        text: "#111827"
      },
      description: {
        text: "#9ca3af"
      },
      expiry: {
        text: "#ca8a04",
        background: "rgba(202, 138, 4, 0.1)"
      },
      originalPrice: {
        text: "#9ca3af"
      },
      salePrice: {
        text: "#16a34a"
      }
    },

    couponModal: {
      overlay: {
        background: "rgba(0, 0, 0, 0.5)"
      },
      card: {
        background: "#ffffff",
        border: "#e5e7eb",
        shadow: "0 20px 60px rgba(0, 0, 0, 0.2)"
      },
      header: {
        background: "linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)",
        border: "#e5e7eb"
      },
      closeButton: {
        color: "#6b7280",
        hover: "#111827"
      },
      code: {
        text: "#15803d",
        background: "rgba(21, 128, 61, 0.1)",
        border: "#dcfce7"
      },
      copyButton: {
        background: "#16a34a",
        text: "#ffffff",
        border: "transparent",
        hover: "#15803d"
      },
      actionButton: {
        background: "#2563eb",
        text: "#ffffff",
        border: "transparent",
        hover: "#1d4ed8"
      },
      discountBadge: {
        background: "linear-gradient(to right, #dc2626, #b91c1c)",
        text: "#ffffff",
        border: "transparent"
      },
      brandName: {
        text: "#6b7280"
      },
      couponName: {
        text: "#111827"
      },
      description: {
        text: "#9ca3af"
      },
      expiry: {
        text: "#ca8a04",
        background: "rgba(202, 138, 4, 0.1)"
      },
      terms: {
        text: "#6b7280",
        background: "#fafafa"
      }
    },

    dealModal: {
      overlay: {
        background: "rgba(0, 0, 0, 0.5)"
      },
      card: {
        background: "#ffffff",
        border: "#e5e7eb",
        shadow: "0 20px 60px rgba(0, 0, 0, 0.2)"
      },
      header: {
        background: "linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)",
        border: "#e5e7eb"
      },
      closeButton: {
        color: "#6b7280",
        hover: "#111827"
      },
      actionButton: {
        background: "#15803d",
        text: "#ffffff",
        border: "transparent",
        hover: "#15803d"
      },
      discountBadge: {
        background: "linear-gradient(to right, #dc2626, #b91c1c)",
        text: "#ffffff",
        border: "transparent"
      },
      brandName: {
        text: "#6b7280"
      },
      dealName: {
        text: "#111827"
      },
      description: {
        text: "#9ca3af"
      },
      expiry: {
        text: "#ca8a04",
        background: "rgba(202, 138, 4, 0.1)"
      },
      originalPrice: {
        text: "#9ca3af"
      },
      salePrice: {
        text: "#16a34a"
      },
      terms: {
        text: "#6b7280",
        background: "#fafafa"
      }
    },

    commonButton: {
      primary: {
        background: "#16a34a",
        text: "#ffffff",
        border: "transparent",
        hover: "#15803d",
        disabled: {
          background: "#d1d5db",
          text: "#9ca3af"
        }
      },
      secondary: {
        background: "#2563eb",
        text: "#ffffff",
        border: "transparent",
        hover: "#1d4ed8",
        disabled: {
          background: "#d1d5db",
          text: "#9ca3af"
        }
      },
      tertiary: {
        background: "transparent",
        text: "#16a34a",
        border: "#16a34a",
        hover: "#dcfce7",
        disabled: {
          background: "transparent",
          text: "#9ca3af"
        }
      }
    },

    searchBox: {
      container: {
        background: "#ffffff",
        border: "#e5e7eb",
        shadow: "0 2px 8px rgba(0, 0, 0, 0.08)",
        focus: {
          border: "#16a34a",
          shadow: "0 0 0 3px rgba(34, 197, 94, 0.2)"
        }
      },
      input: {
        text: "#111827",
        placeholder: "#9ca3af",
        background: "transparent"
      },
      icon: {
        color: "#16a34a",
        hover: "#15803d"
      },
      button: {
        background: "#16a34a",
        text: "#ffffff",
        hover: "#15803d"
      }
    },

    newsletter: {
      container: {
        background: "linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)",
        border: "transparent"
      },
      title: {
        text: "#111827"
      },
      description: {
        text: "#374151"
      },
      input: {
        background: "#ffffff",
        text: "#111827",
        placeholder: "#9ca3af",
        border: "#e5e7eb",
        focus: {
          border: "#16a34a",
          shadow: "0 0 0 3px rgba(34, 197, 94, 0.2)"
        }
      },
      button: {
        background: "#16a34a",
        text: "#ffffff",
        border: "transparent",
        hover: "#15803d",
        disabled: {
          background: "#d1d5db",
          text: "#9ca3af"
        }
      },
      successMessage: {
        text: "#16a34a",
        background: "rgba(34, 197, 94, 0.1)"
      },
      errorMessage: {
        text: "#dc2626",
        background: "rgba(220, 38, 38, 0.1)"
      }
    },

    footer: {
      container: {
        background: "#111827",
        border: "#374151"
      },
      section: {
        title: "#ffffff",
        text: "#d1d5db"
      },
      link: {
        text: "#d1d5db",
        hover: "#16a34a"
      },
      icon: {
        color: "#16a34a",
        hover: "#4ade80"
      },
      socialIcon: {
        color: "#d1d5db",
        hover: "#16a34a",
        background: "rgba(34, 197, 94, 0.1)"
      },
      copyright: {
        text: "#9ca3af"
      },
      divider: {
        color: "#374151"
      }
    },

    header: {
      container: {
        background: "#ffffff",
        border: "#e5e7eb",
        shadow: "0 2px 8px rgba(0, 0, 0, 0.08)"
      },
      logo: {
        text: "#111827",
        hover: "#16a34a"
      },
      navLink: {
        text: "#6b7280",
        hover: "#16a34a",
        active: "#16a34a"
      },
      mobileMenuButton: {
        color: "#6b7280",
        hover: "#16a34a"
      },
      mobileMenu: {
        background: "#ffffff",
        border: "#e5e7eb",
        shadow: "0 8px 25px rgba(0, 0, 0, 0.15)"
      }
    },

    homepage: {
      hero: {
        background: "linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)",
        overlay: "rgba(0, 0, 0, 0.1)",
        title: "#111827",
        subtitle: "#374151",
        description: "#6b7280"
      },
      section: {
        background: "#ffffff",
        title: "#111827",
        subtitle: "#374151",
        description: "#6b7280"
      },
      stats: {
        number: "#16a34a",
        label: "#6b7280",
        background: "rgba(34, 197, 94, 0.05)"
      },
      feature: {
        icon: "#16a34a",
        title: "#111827",
        description: "#6b7280",
        background: "#fafafa"
      }
    },

    pagination: {
      container: {
        background: "#ffffff",
        border: "#e5e7eb"
      },
      button: {
        background: "#fafafa",
        text: "#6b7280",
        border: "#e5e7eb",
        hover: "#e5e7eb",
        active: "#16a34a",
        disabled: {
          background: "#f5f5f5",
          text: "#d1d5db"
        }
      },
      info: {
        text: "#6b7280"
      }
    },

    loading: {
      spinner: {
        primary: "#16a34a",
        secondary: "#dcfce7"
      },
      skeleton: {
        background: "#f5f5f5",
        highlight: "#e5e7eb"
      },
      overlay: {
        background: "rgba(255, 255, 255, 0.8)"
      }
    },

    form: {
      label: {
        text: "#111827",
        required: "#dc2626"
      },
      input: {
        background: "#ffffff",
        text: "#111827",
        placeholder: "#9ca3af",
        border: "#e5e7eb",
        focus: {
          border: "#16a34a",
          shadow: "0 0 0 3px rgba(34, 197, 94, 0.2)"
        },
        error: {
          border: "#dc2626",
          text: "#dc2626",
          background: "rgba(220, 38, 38, 0.05)"
        }
      },
      select: {
        background: "#ffffff",
        text: "#111827",
        border: "#e5e7eb",
        arrow: "#6b7280",
        focus: {
          border: "#16a34a",
          shadow: "0 0 0 3px rgba(34, 197, 94, 0.2)"
        }
      },
      checkbox: {
        background: "#ffffff",
        border: "#e5e7eb",
        checked: {
          background: "#16a34a",
          border: "#16a34a",
          checkmark: "#ffffff"
        }
      },
      radio: {
        background: "#ffffff",
        border: "#e5e7eb",
        checked: {
          background: "#ffffff",
          border: "#16a34a",
          dot: "#16a34a"
        }
      }
    },

    notification: {
      success: {
        background: "rgba(34, 197, 94, 0.1)",
        text: "#16a34a",
        border: "#86efac",
        icon: "#16a34a"
      },
      error: {
        background: "rgba(220, 38, 38, 0.1)",
        text: "#dc2626",
        border: "#fca5a5",
        icon: "#dc2626"
      },
      warning: {
        background: "rgba(202, 138, 4, 0.1)",
        text: "#ca8a04",
        border: "#fde047",
        icon: "#ca8a04"
      },
      info: {
        background: "rgba(37, 99, 235, 0.1)",
        text: "#2563eb",
        border: "#93c5fd",
        icon: "#2563eb"
      }
    },

    badge: {
      primary: {
        background: "#16a34a",
        text: "#ffffff",
        border: "transparent"
      },
      secondary: {
        background: "#2563eb",
        text: "#ffffff",
        border: "transparent"
      },
      success: {
        background: "#16a34a",
        text: "#ffffff",
        border: "transparent"
      },
      warning: {
        background: "#ca8a04",
        text: "#ffffff",
        border: "transparent"
      },
      error: {
        background: "#dc2626",
        text: "#ffffff",
        border: "transparent"
      },
      info: {
        background: "#2563eb",
        text: "#ffffff",
        border: "transparent"
      }
    }
  },

  gradients: {
    // 背景渐变 - 使用绿色主题
    hero: 'linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)',
    card: 'linear-gradient(to bottom right, transparent, #f9fafb, #f3f4f6)',
    button: 'linear-gradient(to right, #16a34a, #15803d)',
    discount: 'linear-gradient(to right, #dc2626, #b91c1c)',
    
    // hover状态渐变
    hover: {
      primary: 'linear-gradient(to bottom right, #dcfce7, #bbf7d0, #86efac)',
      secondary: 'linear-gradient(to bottom right, #dbeafe, #bfdbfe, #93c5fd)',
      accent: 'linear-gradient(to bottom right, #f3e8ff, #e9d5ff, #d8b4fe)'
    }
  },

  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px'
  },

  spacing: {
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  },

  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem'
  }
};
