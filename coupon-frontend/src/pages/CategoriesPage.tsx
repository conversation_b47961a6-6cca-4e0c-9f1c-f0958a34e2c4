import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Loading } from '@/components/ui/Loading';
import apiService from '@/services/api';
import staticDataManager from '@/services/staticDataManager';
import { Category } from '@/types/api';
import CategoryIcon from '@/components/ui/CategoryIcon';

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    setLoading(true);
    try {
      // 优先使用静态数据
      const staticCategories = await staticDataManager.getStaticCategories();
      if (staticCategories) {
        setCategories(staticCategories);
      } else {
        // 回退到API调用
        const response = await apiService.getCategories();
        setCategories(response.data || []);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-100 via-blue-50 to-purple-50">
          <div className="absolute top-10 left-10 w-20 h-20 bg-green-200 rounded-full opacity-20 float-animation"></div>
          <div className="absolute top-20 right-20 w-16 h-16 bg-blue-200 rounded-full opacity-30 float-animation" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-10 left-1/4 w-12 h-12 bg-purple-200 rounded-full opacity-25 float-animation" style={{animationDelay: '4s'}}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="gradient-text">Explore</span> Categories
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Discover amazing deals and coupons organized by your favorite categories
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="flex justify-center py-20">
            <div className="ghibli-card p-12 text-center">
              <Loading />
              <p className="text-gray-600 mt-4">Loading categories...</p>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
              {categories && categories.length > 0 ? categories.map((category, index) => (
                <Link
                  key={category.id}
                  to={`/categories/${category.slug}`}
                  className="block"
                >
                  <div
                    className="ghibli-card p-8 text-center cursor-pointer group hover:scale-105 transition-all duration-300 h-full"
                    style={{animationDelay: `${index * 0.1}s`}}
                  >
                    {/* Category Icon */}
                    <div className="mb-6 group-hover:scale-110 transition-transform duration-300 flex justify-center">
                      <CategoryIcon iconName={category.icon} className="w-16 h-16 text-green-500" />
                    </div>

                    {/* Category Name */}
                    <h3 className="text-lg font-bold text-gray-800 mb-3 group-hover:text-green-600 transition-colors">
                      {category.name}
                    </h3>

                    {/* Decorative element */}
                    <div
                      className="w-full h-1 rounded-full mt-4 opacity-60 bg-gradient-to-r from-green-400 to-blue-400"
                    ></div>
                  </div>
                </Link>
              )) : (
                <div className="col-span-full text-center py-12">
                  <div className="ghibli-card p-12">
                    <p className="text-gray-600 text-lg">No categories found</p>
                    <p className="text-gray-500 mt-2">Please try again later</p>
                  </div>
                </div>
              )}
            </div>

            {!loading && categories.length === 0 && (
              <div className="text-center py-20">
                <div className="ghibli-card p-12 max-w-md mx-auto">
                  <div className="text-6xl mb-4">📂</div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">No Categories Found</h3>
                  <p className="text-gray-600">Categories will appear here once they're available.</p>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
