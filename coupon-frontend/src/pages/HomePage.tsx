import { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import CouponCard from '@/components/cards/CouponCard';
import DealCard from '@/components/cards/DealCard';
import BrandCard from '@/components/cards/BrandCard';
import CouponModal from '@/components/modals/CouponModal';
import DealModal from '@/components/modals/DealModal';
import BrandModal from '@/components/modals/BrandModal';
import NewsletterSubscription from '@/components/subscription/NewsletterSubscription';
import { Loading } from '@/components/ui/Loading';
import CategoryIcon from '@/components/ui/CategoryIcon';
import { useContentConfig } from '@/config/utils';
import apiService from '@/services/api';
import staticDataManager from '@/services/staticDataManager';
import inlineStaticDataManager from '@/services/inlineStaticData';
import { Category, Brand, Coupon, Deal } from '@/types/api';
import { useHomeSEO } from '@/hooks/useSEO';

export default function Home() {
  const contentConfig = useContentConfig();

  // SEO优化
  useHomeSEO();
  const [categories, setCategories] = useState<Category[]>([]);
  const [popularBrands, setPopularBrands] = useState<Brand[]>([]);
  const [featuredCoupons, setFeaturedCoupons] = useState<Coupon[]>([]);
  const [featuredDeals, setFeaturedDeals] = useState<Deal[]>([]);
  const [, setCategoryCoupons] = useState<Record<number, { category: Category; coupons: Coupon[] }>>({});
  // categoryCoupons is loaded but not used in UI yet - keeping for consistency with original
  const [fashionCoupons, setFashionCoupons] = useState<Coupon[]>([]);
  const [travelCoupons, setTravelCoupons] = useState<Coupon[]>([]);
  const [fashionCategory, setFashionCategory] = useState<Category | null>(null);
  const [travelCategory, setTravelCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [isDealModalOpen, setIsDealModalOpen] = useState(false);
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);



  useEffect(() => {
    const loadHomeData = async () => {

      try {
        // 优先使用内联静态数据
        const inlineData = inlineStaticDataManager.getHomePageData();
        if (inlineData) {
          setCategories(inlineData.categories);
          setPopularBrands(inlineData.featuredBrands.slice(0, 18));
          setFeaturedCoupons(inlineData.featuredCoupons.slice(0, 10));
          setFeaturedDeals(inlineData.featuredDeals.slice(0, 16));
          setCategoryCoupons(inlineData.categoryCoupons);
          setLoading(false);
          return;
        }

        // 回退到静态文件数据
        const staticCategories = await staticDataManager.getStaticCategories();
        const staticBrands = await staticDataManager.getStaticFeaturedBrands();
        const staticCoupons = await staticDataManager.getStaticFeaturedCoupons();
        const staticDeals = await staticDataManager.getStaticFeaturedDeals();
        const staticCategoryCoupons = await staticDataManager.getStaticAllCategoryCoupons();

        // 检查静态数据是否有效（至少有分类数据）
        const hasValidStaticData = staticCategories && staticCategories.length > 0;

        if (hasValidStaticData) {
          setCategories(staticCategories);

          // 设置品牌数据
          if (staticBrands && staticBrands.length > 0) {
            setPopularBrands(staticBrands.slice(0, 18));
          }

          // 设置优惠券数据
          if (staticCoupons && staticCoupons.length > 0) {
            setFeaturedCoupons(staticCoupons.slice(0, 10));
          }

          // 设置优惠活动数据
          if (staticDeals && staticDeals.length > 0) {
            setFeaturedDeals(staticDeals.slice(0, 16));
          }

          // 设置分类优惠券（每个分类10个）
          if (staticCategoryCoupons && Object.keys(staticCategoryCoupons).length > 0) {
            setCategoryCoupons(staticCategoryCoupons);
          }
          setLoading(false);
          return;
        } else {
          // 回退到API调用

          const apiPromises = [
            apiService.getCategories({ page_size: 100 }),
            apiService.getBrands({ featured: true, page_size: 18 }),
            apiService.getCoupons({ is_featured: true, page_size: 10 }),
            apiService.getDeals({ is_featured: true, page_size: 16 }),
          ];

          // 为API调用添加超时保护
          const apiTimeout = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('API timeout')), 8000);
          });

          const apiResult = await Promise.race([
            Promise.all(apiPromises),
            apiTimeout
          ]) as [
            { data: Category[] },
            { data: Brand[] },
            { data: Coupon[] },
            { data: Deal[] }
          ];

          const [categoriesResponse, brandsResponse, couponsResponse, dealsResponse] = apiResult;

          setCategories(categoriesResponse.data || []);
          setPopularBrands(brandsResponse.data || []);
          setFeaturedCoupons(couponsResponse.data || []);
          setFeaturedDeals(dealsResponse.data || []);
        }

      } catch (error) {
        setCategories([]);
        setPopularBrands([]);
        setFeaturedCoupons([]);
        setFeaturedDeals([]);
        setCategoryCoupons({});
      } finally {

        // 加载特定分类的优惠券
        await loadSpecificCategoryCoupons();

        setLoading(false);
      }
    };

    loadHomeData();
  }, []);

  // 加载特定分类的优惠券
  const loadSpecificCategoryCoupons = async () => {
    try {
      // 优先使用静态数据中的分类
      const staticCategories = await staticDataManager.getStaticCategories();
      let allCategories = staticCategories || [];

      // 如果静态数据没有分类，才调用API
      if (!allCategories || allCategories.length === 0) {
        const categoriesResponse = await apiService.getCategories();
        allCategories = categoriesResponse.data || [];
      }

      // 查找Fashion & Apparel和Travel & Transportation分类
      const fashionCategory = allCategories.find(cat =>
        cat.name.toLowerCase().includes('fashion') ||
        cat.name.toLowerCase().includes('apparel') ||
        cat.slug.includes('fashion')
      );

      const travelCategory = allCategories.find(cat =>
        cat.name.toLowerCase().includes('travel') ||
        cat.name.toLowerCase().includes('transportation') ||
        cat.slug.includes('travel')
      );

      if (fashionCategory) {
        setFashionCategory(fashionCategory);

        // 优先使用静态数据中的分类优惠券
        const staticCategoryCoupons = await staticDataManager.getStaticAllCategoryCoupons();
        if (staticCategoryCoupons && staticCategoryCoupons[fashionCategory.id]) {
          setFashionCoupons(staticCategoryCoupons[fashionCategory.id].coupons.slice(0, 5));
        } else {
          // 回退到API调用
          const fashionCouponsResponse = await apiService.getCoupons({
            category_id: fashionCategory.id,
            page: 1,
            page_size: 5
          });
          setFashionCoupons(fashionCouponsResponse.data || []);
        }
      }

      if (travelCategory) {
        setTravelCategory(travelCategory);

        // 优先使用静态数据中的分类优惠券
        const staticCategoryCoupons = await staticDataManager.getStaticAllCategoryCoupons();
        if (staticCategoryCoupons && staticCategoryCoupons[travelCategory.id]) {
          setTravelCoupons(staticCategoryCoupons[travelCategory.id].coupons.slice(0, 5));
        } else {
          // 回退到API调用
          const travelCouponsResponse = await apiService.getCoupons({
            category_id: travelCategory.id,
            page: 1,
            page_size: 5
          });
          setTravelCoupons(travelCouponsResponse.data || []);
        }
      }
    } catch (error) {
      // Silently fail
    }
  };

  const handleCouponClick = useCallback(async (coupon: Coupon) => {
    try {
      // Use brand from coupon if available, otherwise fetch it
      let brand = coupon.brand || null;
      if (!brand && coupon.brand_id) {
        brand = await apiService.getBrandById(coupon.brand_id);
      }
      setSelectedCoupon(coupon);
      setSelectedBrand(brand);
      setIsCouponModalOpen(true);
    } catch (error) {
      // Silently fail
    }
  }, []);

  const handleDealClick = async (deal: Deal) => {
    try {
      // Use brand from deal if available, otherwise fetch it
      let brand = deal.brand || null;
      if (!brand && deal.brand_id) {
        brand = await apiService.getBrandById(deal.brand_id);
      }
      setSelectedDeal(deal);
      setSelectedBrand(brand);
      setIsDealModalOpen(true);
    } catch (error) {
      // Silently fail
    }
  };

  const handleBrandClick = (brand: Brand) => {
    setSelectedBrand(brand);
    setIsBrandModalOpen(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading size="lg" text="Loading..." />
      </div>
    );
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-green-50 to-yellow-50">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 float-animation"></div>
          <div className="absolute top-20 right-20 w-16 h-16 bg-green-200 rounded-full opacity-30 float-animation" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-10 left-1/4 w-12 h-12 bg-yellow-200 rounded-full opacity-25 float-animation" style={{animationDelay: '4s'}}></div>
          <div className="absolute top-1/2 right-1/3 w-10 h-10 bg-pink-200 rounded-full opacity-20 float-animation" style={{animationDelay: '1s'}}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Popular Brands */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              <span className="gradient-text">{contentConfig.sections.brands.split(' ')[0]}</span> {contentConfig.sections.brands.split(' ').slice(1).join(' ')}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
              Shop from the most trusted and popular brands worldwide
            </p>
          </div>

          <div className="grid grid-cols-2 xs:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 md:gap-4 mt-6">
            {popularBrands && popularBrands.map((brand, index) => (
              <div
                key={brand.id}
                className="float-animation"
                style={{animationDelay: `${index * 0.2}s`}}
              >
                <BrandCard brand={brand} onClick={handleBrandClick} />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Coupons Section - 优先展示 */}
      <section className="py-20 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-50 to-blue-50"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">{contentConfig.sections.featuredCoupons.split(' ')[0]}</span> {contentConfig.sections.featuredCoupons.split(' ').slice(1).join(' ')}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Handpicked exclusive coupons just for you
            </p>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {featuredCoupons && featuredCoupons.map((coupon, index) => (
              <div
                key={coupon.id}
                className="shimmer"
                style={{animationDelay: `${index * 0.3}s`}}
              >
                <CouponCard
                  coupon={coupon}
                  onClick={() => handleCouponClick(coupon)}
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-blue-50"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">{contentConfig.sections.categories.split(' ')[0]}</span> {contentConfig.sections.categories.split(' ').slice(1).join(' ')}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover amazing deals across all your favorite categories
            </p>
          </div>

          <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3 md:gap-4">
            {categories && categories.map((category, index) => (
              <Link
                key={category.id}
                to={`/categories/${category.slug}`}
                className="block"
              >
                <div
                  className="ghibli-card p-2 md:p-4 text-center cursor-pointer group hover:scale-105 active:scale-95 transition-all duration-300 h-[110px] md:h-[150px] flex flex-col justify-between min-h-touch"
                  style={{animationDelay: `${index * 0.1}s`}}
                >
                  <div className="flex justify-center items-center flex-1">
                    <CategoryIcon iconName={category.icon} className="w-8 h-8 md:w-10 md:h-10 text-green-500 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <div className="flex-shrink-0">
                    <h3 className="text-xs md:text-sm font-semibold text-gray-800 group-hover:text-green-600 transition-colors line-clamp-2 px-1 leading-tight">
                      {category.name}
                    </h3>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Deals Section */}
      <section className="py-20 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-orange-50 to-red-50"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">{contentConfig.sections.featuredDeals.split(' ')[0]}</span> {contentConfig.sections.featuredDeals.split(' ').slice(1).join(' ')}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Limited time offers that you cannot miss
            </p>
          </div>

          <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 md:gap-4">
            {featuredDeals && featuredDeals.map((deal, index) => (
              <div
                key={deal.id}
                className="float-animation"
                style={{animationDelay: `${index * 0.15}s`}}
              >
                <DealCard
                  deal={deal}
                  onClick={() => handleDealClick(deal)}
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Category Specific Coupons - 不同布局展示 */}
      <section className="py-20 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-50 to-cyan-50"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">Category</span> Highlights
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Explore top deals in popular categories
            </p>
          </div>

          {/* Travel & Transportation 优惠券 */}
          {travelCategory && travelCoupons.length > 0 && (
            <div className="mb-16">
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold text-gray-800">
                  ✈️ {travelCategory.name} Deals
                </h3>
                <Link
                  to={`/categories/${travelCategory.slug}`}
                  className="text-green-600 hover:text-green-700 font-semibold flex items-center gap-2"
                >
                  View All
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
              <div className="space-y-4">
                {travelCoupons.map((coupon, index) => (
                  <div
                    key={`travel-${coupon.id}`}
                    className="shimmer"
                    style={{animationDelay: `${index * 0.1}s`}}
                  >
                    <CouponCard
                      coupon={coupon}
                      onClick={() => handleCouponClick(coupon)}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Fashion & Apparel 优惠券 */}
          {fashionCategory && fashionCoupons.length > 0 && (
            <div className="mb-16">
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold text-gray-800">
                  👗 {fashionCategory.name} Collection
                </h3>
                <Link
                  to={`/categories/${fashionCategory.slug}`}
                  className="text-green-600 hover:text-green-700 font-semibold flex items-center gap-2"
                >
                  View All
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
              <div className="space-y-4">
                {fashionCoupons.map((coupon, index) => (
                  <div
                    key={`fashion-${coupon.id}`}
                    className="shimmer"
                    style={{animationDelay: `${index * 0.1}s`}}
                  >
                    <CouponCard
                      coupon={coupon}
                      onClick={() => handleCouponClick(coupon)}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Subscription */}
      <section className="py-20 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-50 to-white"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <NewsletterSubscription />
        </div>
      </section>

      {/* Brand Modal */}
      <BrandModal
        isOpen={isBrandModalOpen}
        onClose={() => setIsBrandModalOpen(false)}
        brand={selectedBrand}
      />

      {/* Coupon Modal */}
      <CouponModal
        isOpen={isCouponModalOpen}
        onClose={() => setIsCouponModalOpen(false)}
        coupon={selectedCoupon}
        brand={selectedBrand}
      />

      {/* Deal Modal */}
      <DealModal
        isOpen={isDealModalOpen}
        onClose={() => setIsDealModalOpen(false)}
        deal={selectedDeal}
        brand={selectedBrand}
      />
    </div>
  );
}
