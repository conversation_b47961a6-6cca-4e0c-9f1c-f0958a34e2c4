import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import BrandCard from '@/components/cards/BrandCard';
import BrandModal from '@/components/modals/BrandModal';
import { Loading } from '@/components/ui/Loading';
import Pagination from '@/components/ui/Pagination';
import apiService from '@/services/api';
import staticDataManager from '@/services/staticDataManager';
import { Brand, Category } from '@/types/api';
import { useBrandsListSEO } from '@/hooks/useSEO';
import { useContentConfig } from '@/config/utils';

export default function BrandsPage() {
  const [searchParams] = useSearchParams();
  const router = useNavigate(); // Using useNavigate instead of Next.js useRouter
  const contentConfig = useContentConfig();

  // SEO优化
  useBrandsListSEO();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null); // 改为string存储slug
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null); // 用于API调用
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // 优先使用静态数据
        const staticCategories = await staticDataManager.getStaticCategories();
        if (staticCategories && staticCategories.length > 0) {
          setCategories(staticCategories);
        } else {
          // 回退到API
          const response = await apiService.getCategories();
          setCategories(response.data || []);
        }
      } catch (error) {
      }
    };

    fetchCategories();
  }, []);

  // 从URL参数初始化状态
  useEffect(() => {
    const query = searchParams.get('q') || '';
    const categorySlug = searchParams.get('category');
    const page = searchParams.get('page');
    setSearchQuery(query);
    setSelectedCategory(categorySlug);
    setCurrentPage(page ? parseInt(page) : 1);
  }, [searchParams]);

  // 当分类改变时，找到对应的ID
  useEffect(() => {
    if (selectedCategory && categories.length > 0) {
      const category = categories.find(cat => cat.slug === selectedCategory);
      setSelectedCategoryId(category ? category.id : null);
    } else {
      setSelectedCategoryId(null);
    }
  }, [selectedCategory, categories]);

  // 更新URL参数的函数
  const updateURL = (params: { q?: string; category?: string | null; page?: number }) => {
    const newSearchParams = new URLSearchParams();

    if (params.q) {
      newSearchParams.set('q', params.q);
    }
    if (params.category) {
      newSearchParams.set('category', params.category);
    }
    if (params.page && params.page > 1) {
      newSearchParams.set('page', params.page.toString());
    }

    const newURL = newSearchParams.toString() ? `?${newSearchParams.toString()}` : '/brands';
    router(newURL); // Using navigate instead of router.push
  };

  useEffect(() => {
    const fetchBrands = async () => {
      setLoading(true);
      try {
        const params = {
          page: currentPage,
          page_size: 20,
          ...(selectedCategoryId && { category_id: selectedCategoryId }),
          ...(searchQuery && { search: searchQuery }),
        };

        // 优先尝试使用静态数据（前5页且无筛选条件）
        const staticResponse = await staticDataManager.getStaticBrandListPage(currentPage, params);

        if (staticResponse) {
          setBrands(staticResponse.data || []);
          setTotalPages(staticResponse.total_pages || 1);
        } else {
          const response = await apiService.getBrands(params);
          setBrands(response.data || []);
          setTotalPages(response.total_pages || 1);
        }
      } catch (error) {
        setBrands([]);
      } finally {
        setLoading(false);
      }
    };

    fetchBrands();
  }, [searchQuery, selectedCategoryId, currentPage]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    updateURL({ q: searchQuery, category: selectedCategory, page: 1 });
  };

  const handleBrandClick = (brand: Brand) => {
    setSelectedBrand(brand);
    setIsBrandModalOpen(true);
  };

  const handleCategoryFilter = (categorySlug: string | null) => {
    updateURL({ q: searchQuery, category: categorySlug, page: 1 });
  };

  const handlePageChange = (page: number) => {
    updateURL({ q: searchQuery, category: selectedCategory, page });
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-purple-50 to-green-50">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 float-animation"></div>
          <div className="absolute top-20 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-30 float-animation" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-10 left-1/4 w-12 h-12 bg-green-200 rounded-full opacity-25 float-animation" style={{animationDelay: '4s'}}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="gradient-text">{contentConfig.pageContent.brands.title.split(' ')[0]}</span> {contentConfig.pageContent.brands.title.split(' ').slice(1).join(' ')}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            {contentConfig.pageContent.brands.subtitle}
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="mb-12">
          <div className="ghibli-card p-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{contentConfig.pageContent.brands.filterTitle}</h2>

            {/* Search */}
            <form onSubmit={handleSearch} className="max-w-2xl mx-auto mb-8">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search for brands..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-white/70 backdrop-blur-sm border-2 border-white/30 rounded-full px-6 py-4 pl-12 focus:border-green-300 focus:ring-4 focus:ring-green-100 focus:outline-none transition-all duration-300 placeholder-gray-500"
                />
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </form>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-3">
              <button
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  selectedCategory === null
                    ? 'nature-button'
                    : 'bg-white/70 text-gray-700 hover:bg-white hover:shadow-md'
                }`}
                onClick={() => handleCategoryFilter(null)}
              >
                All Categories
              </button>
              {categories && categories.map((category) => (
                <button
                  key={category.id}
                  className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                    selectedCategory === category.slug
                      ? 'nature-button'
                      : 'bg-white/70 text-gray-700 hover:bg-white hover:shadow-md'
                  }`}
                  onClick={() => handleCategoryFilter(category.slug)}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Brands Grid */}
        {loading ? (
          <div className="flex justify-center py-20">
            <div className="ghibli-card p-12 text-center">
              <Loading />
              <p className="text-gray-600 mt-4">Loading amazing brands...</p>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 mb-12">
              {brands && brands.length > 0 ? brands.map((brand, index) => (
                <div
                  key={brand.id}
                  className="float-animation"
                  style={{animationDelay: `${index * 0.1}s`}}
                >
                  <BrandCard brand={brand} onClick={handleBrandClick} />
                </div>
              )) : (
                <div className="col-span-full text-center py-12">
                  <div className="ghibli-card p-12">
                    <p className="text-gray-600 text-lg">No brands found</p>
                    <p className="text-gray-500 mt-2">Try adjusting your search or filter criteria</p>
                  </div>
                </div>
              )}
            </div>

            {/* Pagination */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              className="mt-12"
            />
          </>
        )}

        {!loading && brands.length === 0 && (
          <div className="text-center py-20">
            <div className="ghibli-card p-12 max-w-md mx-auto">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">{contentConfig.pageContent.brands.noResultsTitle}</h3>
              <p className="text-gray-600">{contentConfig.pageContent.brands.noResultsMessage}</p>
            </div>
          </div>
        )}
      </div>

      {/* Brand Modal */}
      <BrandModal
        isOpen={isBrandModalOpen}
        onClose={() => setIsBrandModalOpen(false)}
        brand={selectedBrand}
      />
    </div>
  );
}
