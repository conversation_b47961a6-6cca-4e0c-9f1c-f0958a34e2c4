import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import BrandCard from '@/components/cards/BrandCard';
import CouponCard from '@/components/cards/CouponCard';
import DealCard from '@/components/cards/DealCard';
import CouponModal from '@/components/modals/CouponModal';
import DealModal from '@/components/modals/DealModal';
import BrandModal from '@/components/modals/BrandModal';
import { Loading } from '@/components/ui/Loading';
import Pagination from '@/components/ui/Pagination';
import CategoryIcon from '@/components/ui/CategoryIcon';
import apiService from '@/services/api';
import staticDataManager from '@/services/staticDataManager';
import { Category, Brand, Coupon, Deal } from '@/types/api';

export default function CategoryDetailPage() {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();

  const [category, setCategory] = useState<Category | null>(null);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'brands' | 'coupons' | 'deals'>('brands');

  // 分页状态
  const [brandsPage, setBrandsPage] = useState(1);
  const [couponsPage, setCouponsPage] = useState(1);
  const [dealsPage, setDealsPage] = useState(1);
  const [brandsTotalPages, setBrandsTotalPages] = useState(1);
  const [couponsTotalPages, setCouponsTotalPages] = useState(1);
  const [dealsTotalPages, setDealsTotalPages] = useState(1);
  const [tabLoading, setTabLoading] = useState(false);

  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [isDealModalOpen, setIsDealModalOpen] = useState(false);
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);

  // 在静态模式下，从URL hash获取tab状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const hash = window.location.hash.replace('#', '');
      if (hash && ['brands', 'coupons', 'deals'].includes(hash)) {
        setActiveTab(hash as 'brands' | 'coupons' | 'deals');
      }
    }
  }, []);

  // 更新tab状态的函数（静态模式下使用hash）
  const updateTab = (tab: 'brands' | 'coupons' | 'deals') => {
    setActiveTab(tab);
    // 在静态模式下，使用hash来保存状态
    if (typeof window !== 'undefined') {
      if (tab === 'brands') {
        window.location.hash = '';
      } else {
        window.location.hash = tab;
      }
    }

    // 当切换tab时，如果当前页不是第1页，则重新获取第1页数据
    if (tab === 'brands' && brandsPage !== 1) {
      fetchBrands(1);
    } else if (tab === 'coupons' && couponsPage !== 1) {
      fetchCoupons(1);
    } else if (tab === 'deals' && dealsPage !== 1) {
      fetchDeals(1);
    }
  };

  useEffect(() => {
    if (slug) {
      fetchCategoryData();
    }
  }, [slug]);

  const fetchCategoryData = async () => {
    setLoading(true);
    try {
      // 优先使用静态数据获取分类
      let allCategories = await staticDataManager.getStaticCategories();
      if (!allCategories) {
        // 回退到API调用
        const categoriesResponse = await apiService.getCategories();
        allCategories = categoriesResponse.data;
      }
      const categoryData = allCategories.find(cat => cat.slug === slug);

      if (!categoryData) {
        setCategory(null);
        setLoading(false);
        return;
      }

      setCategory(categoryData);

      // 并行获取分类相关的数据（第一页）
      const [brandsResponse, couponsResponse, dealsResponse] = await Promise.all([
        apiService.getBrands({ category_id: categoryData.id, page: 1, page_size: 20 }),
        apiService.getCoupons({ category_id: categoryData.id, page: 1, page_size: 20 }),
        apiService.getDeals({ category_id: categoryData.id, page: 1, page_size: 20 })
      ]);


      setBrands(brandsResponse.data || []);
      setCoupons(couponsResponse.data || []);
      setDeals(dealsResponse.data || []);

      // 设置分页信息
      setBrandsTotalPages(brandsResponse.total_pages || 1);
      setCouponsTotalPages(couponsResponse.total_pages || 1);
      setDealsTotalPages(dealsResponse.total_pages || 1);
    } catch (error) {
      setCategory(null);
    } finally {
      setLoading(false);
    }
  };

  // 获取品牌数据（分页）
  const fetchBrands = async (page: number) => {
    if (!category) return;
    setTabLoading(true);
    try {
      const response = await apiService.getBrands({
        category_id: category.id,
        page,
        page_size: 20
      });
      setBrands(response.data || []);
      setBrandsTotalPages(response.total_pages || 1);
      setBrandsPage(page);
    } catch (error) {
    } finally {
      setTabLoading(false);
    }
  };

  // 获取优惠券数据（分页）
  const fetchCoupons = async (page: number) => {
    if (!category) return;
    setTabLoading(true);
    try {
      const response = await apiService.getCoupons({
        category_id: category.id,
        page,
        page_size: 20
      });
      setCoupons(response.data || []);
      setCouponsTotalPages(response.total_pages || 1);
      setCouponsPage(page);
    } catch (error) {
    } finally {
      setTabLoading(false);
    }
  };

  // 获取优惠活动数据（分页）
  const fetchDeals = async (page: number) => {
    if (!category) return;
    setTabLoading(true);
    try {
      const response = await apiService.getDeals({
        category_id: category.id,
        page,
        page_size: 20
      });
      setDeals(response.data || []);
      setDealsTotalPages(response.total_pages || 1);
      setDealsPage(page);
    } catch (error) {
    } finally {
      setTabLoading(false);
    }
  };

  const handleCouponClick = async (coupon: Coupon) => {
    try {
      let brand = coupon.brand || null;
      if (!brand && coupon.brand_id) {
        brand = await apiService.getBrandById(coupon.brand_id);
      }
      setSelectedCoupon(coupon);
      setSelectedBrand(brand);
      setIsCouponModalOpen(true);
    } catch (error) {
    }
  };

  const handleBrandClick = (brand: Brand) => {
    setSelectedBrand(brand);
    setIsBrandModalOpen(true);
  };

  const handleDealClick = async (deal: Deal) => {
    try {
      let brand = deal.brand || null;
      if (!brand && deal.brand_id) {
        brand = await apiService.getBrandById(deal.brand_id);
      }
      setSelectedDeal(deal);
      setSelectedBrand(brand);
      setIsDealModalOpen(true);
    } catch (error) {
    }
  };

  if (!slug) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-4">❌</div>
          <p className="text-gray-600">Invalid category URL</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading size="lg" text="Loading category..." />
      </div>
    );
  }

  if (!category) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-4">🔍</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Category Not Found</h2>
          <p className="text-gray-600 mb-4">The category you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/categories')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Browse All Categories
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-100 via-blue-50 to-purple-50">
          <div className="absolute top-10 left-10 w-20 h-20 bg-green-200 rounded-full opacity-20 float-animation"></div>
          <div className="absolute top-20 right-20 w-16 h-16 bg-blue-200 rounded-full opacity-30 float-animation" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-10 left-1/4 w-12 h-12 bg-purple-200 rounded-full opacity-25 float-animation" style={{animationDelay: '4s'}}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Category Icon */}
            <div className="mb-6">
              <div className="w-32 h-32 mx-auto bg-white rounded-full shadow-lg p-8 ghibli-card">
                <CategoryIcon iconName={category.icon} className="w-full h-full text-green-500" />
              </div>
            </div>

            {/* Category Name */}
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              <span className="gradient-text">{category.name}</span>
            </h1>

            {/* Description */}
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover amazing deals, coupons, and brands in the {category.name.toLowerCase()} category
            </p>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="flex justify-center mb-8">
          <div className="ghibli-card p-2 inline-flex">
            <button
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === 'brands'
                  ? 'nature-button'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => updateTab('brands')}
            >
              Brands
            </button>
            <button
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === 'coupons'
                  ? 'nature-button'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => updateTab('coupons')}
            >
              Coupons
            </button>
            <button
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === 'deals'
                  ? 'nature-button'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => updateTab('deals')}
            >
              Deals
            </button>
          </div>
        </div>

        {/* Content */}
        {activeTab === 'brands' && (
          <div>
            {tabLoading ? (
              <div className="flex justify-center py-12">
                <Loading />
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 mb-12">
                  {brands.length > 0 ? (
                    brands.map((brand, index) => (
                      <div
                        key={brand.id}
                        className="float-animation"
                        style={{animationDelay: `${index * 0.1}s`}}
                      >
                        <BrandCard brand={brand} onClick={handleBrandClick} />
                      </div>
                    ))
                  ) : (
                    <div className="col-span-full text-center py-12">
                      <div className="ghibli-card p-12">
                        <div className="text-6xl mb-4">🏪</div>
                        <h3 className="text-xl font-bold text-gray-800 mb-2">No Brands Available</h3>
                        <p className="text-gray-600">This category doesn't have any brands at the moment.</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Brands Pagination */}
                {brandsTotalPages > 1 && (
                  <Pagination
                    currentPage={brandsPage}
                    totalPages={brandsTotalPages}
                    onPageChange={fetchBrands}
                    className="mt-4"
                  />
                )}
              </>
            )}
          </div>
        )}

        {activeTab === 'coupons' && (
          <div>
            {tabLoading ? (
              <div className="flex justify-center py-12">
                <Loading />
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 gap-4 mb-12">
                  {coupons.length > 0 ? (
                    coupons.map((coupon, index) => (
                      <div
                        key={coupon.id}
                        className="shimmer"
                        style={{animationDelay: `${index * 0.1}s`}}
                      >
                        <CouponCard
                          coupon={coupon}
                          onClick={() => handleCouponClick(coupon)}
                        />
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <div className="ghibli-card p-12">
                        <div className="text-6xl mb-4">🎫</div>
                        <h3 className="text-xl font-bold text-gray-800 mb-2">No Coupons Available</h3>
                        <p className="text-gray-600">This category doesn't have any coupons at the moment.</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Coupons Pagination */}
                {couponsTotalPages > 1 && (
                  <Pagination
                    currentPage={couponsPage}
                    totalPages={couponsTotalPages}
                    onPageChange={fetchCoupons}
                    className="mt-4"
                  />
                )}
              </>
            )}
          </div>
        )}

        {activeTab === 'deals' && (
          <div>
            {tabLoading ? (
              <div className="flex justify-center py-12">
                <Loading />
              </div>
            ) : (
              <>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4 mb-12">
                  {deals.length > 0 ? (
                    deals.map((deal, index) => (
                      <div
                        key={deal.id}
                        className="float-animation"
                        style={{animationDelay: `${index * 0.1}s`}}
                      >
                        <DealCard
                          deal={deal}
                          onClick={() => handleDealClick(deal)}
                        />
                      </div>
                    ))
                  ) : (
                    <div className="col-span-full text-center py-12">
                      <div className="ghibli-card p-12">
                        <div className="text-6xl mb-4">🎯</div>
                        <h3 className="text-xl font-bold text-gray-800 mb-2">No Deals Available</h3>
                        <p className="text-gray-600">This category doesn't have any deals at the moment.</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Deals Pagination */}
                {dealsTotalPages > 1 && (
                  <Pagination
                    currentPage={dealsPage}
                    totalPages={dealsTotalPages}
                    onPageChange={fetchDeals}
                    className="mt-4"
                  />
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <CouponModal
        isOpen={isCouponModalOpen}
        onClose={() => setIsCouponModalOpen(false)}
        coupon={selectedCoupon}
        brand={selectedBrand}
      />

      <DealModal
        isOpen={isDealModalOpen}
        onClose={() => setIsDealModalOpen(false)}
        deal={selectedDeal}
        brand={selectedBrand}
      />

      <BrandModal
        isOpen={isBrandModalOpen}
        onClose={() => setIsBrandModalOpen(false)}
        brand={selectedBrand}
      />
    </div>
  );
}
