import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import BrandCard from '@/components/cards/BrandCard';
import CouponCard from '@/components/cards/CouponCard';
import DealCard from '@/components/cards/DealCard';
import CouponModal from '@/components/modals/CouponModal';
import DealModal from '@/components/modals/DealModal';
import BrandModal from '@/components/modals/BrandModal';
import { Loading } from '@/components/ui/Loading';
import Pagination from '@/components/ui/Pagination';
import apiService from '@/services/api';
import { SearchResponse, Coupon, Deal, Brand } from '@/types/api';

export default function SearchPage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const query = searchParams.get('q') || '';

  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState(query);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [isDealModalOpen, setIsDealModalOpen] = useState(false);
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);

  useEffect(() => {
    if (query) {
      const page = parseInt(searchParams.get('page') || '1');
      setCurrentPage(page);
      performSearch(query, page);
    } else {
      setLoading(false);
    }
  }, [query, searchParams]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('page', page.toString());
    navigate(`/search?${newSearchParams.toString()}`);
  };

  const performSearch = async (searchTerm: string, page: number = 1) => {
    setLoading(true);
    try {
      const results = await apiService.search({ query: searchTerm, page, page_size: 20 });
      setSearchResults(results);
      setTotalResults(results?.total || 0);
      setCurrentPage(page);

      // 计算总页数（基于最大的分页数据）
      const maxTotal = Math.max(
        results?.brands?.total || 0,
        results?.coupons?.total || 0,
        results?.deals?.total || 0
      );
      setTotalPages(Math.ceil(maxTotal / 20));
    } catch (error) {
      setSearchResults({
        query: searchTerm,
        type: 'all',
        total: 0,
        page: page,
        page_size: 20
      });
      setTotalResults(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleCouponClick = async (coupon: Coupon) => {
    try {
      // Use brand from coupon if available, otherwise fetch it
      let brand = coupon.brand || null;
      if (!brand && coupon.brand_id) {
        brand = await apiService.getBrandById(coupon.brand_id);
      }
      setSelectedCoupon(coupon);
      setSelectedBrand(brand);
      setIsCouponModalOpen(true);
    } catch (error) {
    }
  };

  const handleBrandClick = (brand: Brand) => {
    setSelectedBrand(brand);
    setIsBrandModalOpen(true);
  };

  const handleDealClick = async (deal: Deal) => {
    try {
      // Use brand from deal if available, otherwise fetch it
      let brand = deal.brand || null;
      if (!brand && deal.brand_id) {
        brand = await apiService.getBrandById(deal.brand_id);
      }
      setSelectedDeal(deal);
      setSelectedBrand(brand);
      setIsDealModalOpen(true);
    } catch (error) {
    }
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-50">
          <div className="absolute top-10 left-10 w-20 h-20 bg-indigo-200 rounded-full opacity-20 float-animation"></div>
          <div className="absolute top-20 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-30 float-animation" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-10 left-1/4 w-12 h-12 bg-pink-200 rounded-full opacity-25 float-animation" style={{animationDelay: '4s'}}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Search Results for "<span className="gradient-text">{query}</span>"
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Found {totalResults} magical results for you ✨
          </p>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
            <div className="ghibli-card p-2">
              <div className="flex items-center">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    placeholder="Search for more treasures..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full bg-transparent border-none outline-none text-lg pl-12 pr-6 py-3 placeholder-gray-500"
                  />
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
                <button
                  type="submit"
                  className="nature-button ml-2 px-6 py-3 text-base font-semibold"
                >
                  Search ✨
                </button>
              </div>
            </div>
          </form>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {loading ? (
          <div className="flex justify-center py-20">
            <div className="ghibli-card p-12 text-center">
              <Loading />
              <p className="text-gray-600 mt-4">Searching for magical results...</p>
            </div>
          </div>
        ) : searchResults ? (
          <div className="space-y-16">
            {/* Brands Section */}
            {searchResults.brands && searchResults.brands.brand_list && searchResults.brands.brand_list.length > 0 && (
              <section>
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold mb-4">
                    <span className="gradient-text">Brands</span> ({searchResults.brands.total})
                  </h2>
                  <p className="text-gray-600">Amazing brands matching your search</p>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                  {searchResults.brands.brand_list.map((brand, index) => (
                    <div
                      key={brand.id}
                      className="float-animation"
                      style={{animationDelay: `${index * 0.1}s`}}
                    >
                      <BrandCard brand={brand} onClick={handleBrandClick} />
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Coupons Section */}
            {searchResults.coupons && searchResults.coupons.coupon_list && searchResults.coupons.coupon_list.length > 0 && (
              <section>
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold mb-4">
                    <span className="gradient-text">Coupons</span> ({searchResults.coupons.total})
                  </h2>
                  <p className="text-gray-600">Exclusive coupon codes just for you</p>
                </div>
                <div className="grid grid-cols-1 gap-4">
                  {searchResults.coupons.coupon_list.map((coupon, index) => (
                    <div
                      key={coupon.id}
                      className="shimmer"
                      style={{animationDelay: `${index * 0.1}s`}}
                    >
                      <CouponCard
                        coupon={coupon}
                        onClick={() => handleCouponClick(coupon)}
                      />
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Deals Section */}
            {searchResults.deals && searchResults.deals.deal_list && searchResults.deals.deal_list.length > 0 && (
              <section>
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold mb-4">
                    <span className="gradient-text">Deals</span> ({searchResults.deals.total})
                  </h2>
                  <p className="text-gray-600">Hot deals you don't want to miss</p>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
                  {searchResults.deals.deal_list.map((deal, index) => (
                    <div
                      key={deal.id}
                      className="float-animation"
                      style={{animationDelay: `${index * 0.1}s`}}
                    >
                      <DealCard
                        deal={deal}
                        onClick={() => handleDealClick(deal)}
                      />
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* No Results */}
            {searchResults.total === 0 && query && (
              <div className="text-center py-12">
                <div className="ghibli-card p-12 max-w-md mx-auto">
                  <div className="text-6xl mb-4">🔍</div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    No results found
                  </h2>
                  <p className="text-gray-600 mb-8">
                    Try searching with different keywords or browse our categories.
                  </p>
                </div>
              </div>
            )}

            {/* Pagination */}
            {searchResults && searchResults.total > 0 && totalPages > 1 && (
              <div className="mt-16">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="ghibli-card p-12 max-w-md mx-auto">
              <div className="text-6xl mb-4">🔍</div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Start your search
              </h2>
              <p className="text-gray-600">
                Enter a search term to find brands, coupons, and deals.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* 底部间距，防止内容覆盖页脚 */}
      <div className="h-20"></div>

      {/* Coupon Modal */}
      <CouponModal
        isOpen={isCouponModalOpen}
        onClose={() => setIsCouponModalOpen(false)}
        coupon={selectedCoupon}
        brand={selectedBrand}
      />

      {/* Deal Modal */}
      <DealModal
        isOpen={isDealModalOpen}
        onClose={() => setIsDealModalOpen(false)}
        deal={selectedDeal}
        brand={selectedBrand}
      />

      {/* Brand Modal */}
      <BrandModal
        isOpen={isBrandModalOpen}
        onClose={() => setIsBrandModalOpen(false)}
        brand={selectedBrand}
      />
    </div>
  );
}
