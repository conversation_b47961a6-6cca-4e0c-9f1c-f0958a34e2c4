import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AsyncImage from '@/components/ui/AsyncImage';
import CouponCard from '@/components/cards/CouponCard';
import DealCard from '@/components/cards/DealCard';
import CouponModal from '@/components/modals/CouponModal';
import DealModal from '@/components/modals/DealModal';
import { Loading } from '@/components/ui/Loading';
import apiService from '@/services/api';
import { Brand, Coupon, Deal } from '@/types/api';
import { getImageUrl } from '@/lib/utils';
// import { useBrandSEO, useNotFoundSEO } from '@/hooks/useSEO';

export default function BrandDetailPage() {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();

  const [brand, setBrand] = useState<Brand | null>(null);
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'coupons' | 'deals'>('coupons');
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [isDealModalOpen, setIsDealModalOpen] = useState(false);

  // 在静态模式下，从URL hash获取tab状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const hash = window.location.hash.replace('#', '');
      if (hash && ['coupons', 'deals'].includes(hash)) {
        setActiveTab(hash as 'coupons' | 'deals');
      }
    }
  }, []);

  // 更新tab状态的函数（静态模式下使用hash）
  const updateTab = (tab: 'coupons' | 'deals') => {
    setActiveTab(tab);
    // 在静态模式下，使用hash来保存状态
    if (typeof window !== 'undefined') {
      if (tab === 'coupons') {
        window.location.hash = '';
      } else {
        window.location.hash = tab;
      }
    }
  };

  const fetchBrandData = useCallback(async () => {
    setLoading(true);
    try {
      // Find brand by unique_name using dedicated API
      const brandData = await apiService.getBrandByUniqueName(slug!);

      if (!brandData) {
        setBrand(null);
        setLoading(false);
        return;
      }

      setBrand(brandData);

      // Fetch brand content
      const [couponsResponse, dealsResponse] = await Promise.all([
        apiService.getCoupons({ brand_id: brandData.id, page_size: 50 }),
        apiService.getDeals({ brand_id: brandData.id, page_size: 50 })
      ]);


      setCoupons(couponsResponse.data || []);
      setDeals(dealsResponse.data || []);
    } catch (error) {
      setBrand(null);
    } finally {
      setLoading(false);
    }
  }, [slug]);

  useEffect(() => {
    if (slug) {
      fetchBrandData();
    }
  }, [slug, fetchBrandData]);

  // 设置页面标题
  useEffect(() => {
    if (brand) {
      document.title = `${brand.name} Coupons - GoCoupons`;
    } else if (!loading && slug) {
      document.title = 'Brand Not Found - GoCoupons';
    }
  }, [brand, loading, slug]);

  const handleCouponClick = async (coupon: Coupon) => {
    try {
      let brandData = brand;
      if (!brandData && coupon.brand_id) {
        brandData = await apiService.getBrandById(coupon.brand_id);
      }
      setSelectedCoupon(coupon);
      setIsCouponModalOpen(true);
    } catch (error) {
    }
  };

  const handleDealClick = async (deal: Deal) => {
    try {
      let brandData = brand;
      if (!brandData && deal.brand_id) {
        brandData = await apiService.getBrandById(deal.brand_id);
      }
      setSelectedDeal(deal);
      setIsDealModalOpen(true);
    } catch (error) {
    }
  };

  if (!slug) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-4">❌</div>
          <p className="text-gray-600">Invalid brand URL</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading size="lg" text="Loading brand..." />
      </div>
    );
  }

  if (!brand) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-4">🔍</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Brand Not Found</h2>
          <p className="text-gray-600 mb-4">The brand you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/brands')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Browse All Brands
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-purple-50 to-green-50">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 float-animation"></div>
          <div className="absolute top-20 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-30 float-animation" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-10 left-1/4 w-12 h-12 bg-green-200 rounded-full opacity-25 float-animation" style={{animationDelay: '4s'}}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Brand Logo */}
            <div className="mb-6">
              <div className="w-32 h-32 mx-auto bg-white rounded-full shadow-lg p-4 ghibli-card">
                {brand.logo ? (
                  <AsyncImage
                    src={getImageUrl(brand.logo)}
                    alt={brand.name}
                    className="w-full h-full object-contain"
                    fallback="/images/brand-placeholder.svg"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-500 text-4xl font-bold">
                      {brand.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Brand Name */}
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              <span className="gradient-text">{brand.name}</span>
            </h1>

            {/* Brand Description */}
            {brand.description && (
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                {brand.description}
              </p>
            )}

            {/* Stats */}
            <div className="flex justify-center space-x-8 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{coupons.length}</div>
                <div className="text-gray-600">Coupons</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{deals.length}</div>
                <div className="text-gray-600">Deals</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="flex justify-center mb-8">
          <div className="ghibli-card p-2 inline-flex">
            <button
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === 'coupons'
                  ? 'nature-button'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => updateTab('coupons')}
            >
              Coupons ({coupons.length})
            </button>
            <button
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === 'deals'
                  ? 'nature-button'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => updateTab('deals')}
            >
              Deals ({deals.length})
            </button>
          </div>
        </div>

        {/* Content */}
        {activeTab === 'coupons' ? (
          <div className="grid grid-cols-1 gap-4">
            {coupons.length > 0 ? (
              coupons.map((coupon, index) => (
                <div
                  key={coupon.id}
                  className="shimmer"
                  style={{animationDelay: `${index * 0.1}s`}}
                >
                  <CouponCard
                    coupon={coupon}
                    onClick={() => handleCouponClick(coupon)}
                  />
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <div className="ghibli-card p-12">
                  <div className="text-6xl mb-4">🎫</div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">No Coupons Available</h3>
                  <p className="text-gray-600">This brand doesn't have any coupons at the moment.</p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
            {deals.length > 0 ? (
              deals.map((deal, index) => (
                <div
                  key={deal.id}
                  className="float-animation"
                  style={{animationDelay: `${index * 0.1}s`}}
                >
                  <DealCard
                    deal={deal}
                    onClick={() => handleDealClick(deal)}
                  />
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <div className="ghibli-card p-12">
                  <div className="text-6xl mb-4">🎯</div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">No Deals Available</h3>
                  <p className="text-gray-600">This brand doesn't have any deals at the moment.</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <CouponModal
        isOpen={isCouponModalOpen}
        onClose={() => setIsCouponModalOpen(false)}
        coupon={selectedCoupon}
        brand={brand}
      />

      <DealModal
        isOpen={isDealModalOpen}
        onClose={() => setIsDealModalOpen(false)}
        deal={selectedDeal}
        brand={brand}
      />
    </div>
  );
}
