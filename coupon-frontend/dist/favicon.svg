<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="16" fill="url(#gradient)"/>
  
  <!-- 优惠券形状 -->
  <rect x="6" y="10" width="20" height="12" rx="2" fill="white"/>
  
  <!-- 左侧圆形缺口 -->
  <circle cx="6" cy="16" r="2" fill="url(#gradient)"/>
  
  <!-- 右侧圆形缺口 -->
  <circle cx="26" cy="16" r="2" fill="url(#gradient)"/>
  
  <!-- 虚线分割线 -->
  <line x1="16" y1="12" x2="16" y2="20" stroke="#10b981" stroke-width="1" stroke-dasharray="1,1"/>
  
  <!-- 百分号 -->
  <text x="11" y="18" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#10b981">%</text>
  
  <!-- 星星装饰 -->
  <polygon points="20,14 21,16 23,16 21.5,17.5 22,19 20,18 18,19 18.5,17.5 17,16 19,16" fill="#fbbf24"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
