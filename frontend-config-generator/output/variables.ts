// 全局变量配置 - 避免重复内容
// 这些变量会在整个网站配置中被引用

export const SITE_VARIABLES = {
  // 基础信息
  SITE_NAME: 'CouponsGo',
  SITE_DOMAIN: 'coupons-go.org',
  SITE_URL: 'https://coupons-go.org',
  API_DOMAIN: 'api.coupons-go.org',
  
  // 联系信息 (可选 - 如果不需要可以注释掉)
  CONTACT_EMAIL: '<EMAIL>',
  
  // 社交媒体 (可选 - 如果不需要可以注释掉)
  // TWITTER_HANDLE: '@gocoupons',
  // TWITTER_URL: 'https://twitter.com/gocoupons',
  // FACEBOOK_URL: 'https://facebook.com/gocoupons',
  // INSTAGRAM_URL: 'https://instagram.com/gocoupons',
  // FACEBOOK_APP_ID: '123456789',
  
  // 技术信息
  APP_VERSION: '1.0.0',
  CURRENT_YEAR: new Date().getFullYear().toString(),
  CURRENT_DATE: new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }),
  
  // 品牌标语
  TAGLINE: 'Save Smart Live Better',
  SHORT_DESCRIPTION: 'Find discounts and deals across various categories',
  
  // 特色功能
  FEATURES: {
    VERIFIED_COUPONS: 'Verified Coupons',
    EXCLUSIVE_DEALS: 'Exclusive Deals',
    PRICE_ALERTS: 'Price Tracker Alerts',
    CASHBACK: 'Cashback Rewards'
  },

  // 环境配置
  ENVIRONMENT: {
    API_BASE_URL_PRODUCTION: 'http://api.coupons-go.org',
    API_BASE_URL_DEVELOPMENT: 'http://localhost:8080',
    IS_PRODUCTION: true
  }
} as const;

// 辅助函数：替换模板中的变量
export const replaceVariables = (template: string): string => {
  let result = template;
  
  // 替换所有 {{VARIABLE_NAME}} 格式的变量
  Object.entries(SITE_VARIABLES).forEach(([key, value]) => {
    if (typeof value === 'string') {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
  });
  
  // 处理嵌套对象
  Object.entries(SITE_VARIABLES.FEATURES).forEach(([key, value]) => {
    const regex = new RegExp(`{{FEATURES.${key}}}`, 'g');
    result = result.replace(regex, value);
  });
  
  return result;
};

// 常用组合变量
export const COMBINED_VARIABLES = {
  FULL_SITE_TITLE: `${SITE_VARIABLES.SITE_NAME} - ${SITE_VARIABLES.TAGLINE}`,
  CONTACT_INFO: SITE_VARIABLES.CONTACT_EMAIL, // 简化为只显示邮箱
  COPYRIGHT: `© ${SITE_VARIABLES.CURRENT_YEAR} ${SITE_VARIABLES.SITE_NAME}. All rights reserved.`,
} as const;
