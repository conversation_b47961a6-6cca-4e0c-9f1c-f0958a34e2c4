package fatcouponvo

type GetMerchantsResp struct {
	Errno  int    `json:"errno"`
	Errmsg string `json:"errmsg"`
	Data   struct {
		Count int `json:"count"`
		Data  []struct {
			Priority          int     `json:"priority"`
			Description       string  `json:"description"`
			SpecialNote       string  `json:"specialNote"`
			Tip               string  `json:"tip"`
			Image             string  `json:"image"`
			Logo              string  `json:"logo"`
			Background        string  `json:"background"`
			CouponCount       int     `json:"couponCount"`
			MaxDiscount       string  `json:"maxDiscount"`
			MinDiscount       string  `json:"minDiscount"`
			Content           string  `json:"content"`
			AppOpenMode       string  `json:"appOpenMode"`
			IsHidePromoCodes  bool    `json:"isHidePromoCodes"`
			IsHideCouponCodes bool    `json:"isHideCouponCodes"`
			IsHideOffers      bool    `json:"isHideOffers"`
			Vote              float64 `json:"vote"`
			VoteCount         int     `json:"voteCount"`
			Name              string  `json:"name"`
			Domain            string  `json:"domain"`
			Categories        []struct {
				Id         string `json:"_id"`
				CategoryId string `json:"categoryId"`
				Id1        string `json:"id"`
				Name       string `json:"name"`
				Slug       string `json:"slug"`
			} `json:"categories"`
			ImageId        string `json:"imageId"`
			Slug           string `json:"slug"`
			BackgroundId   string `json:"backgroundId,omitempty"`
			IsNoCashback   bool   `json:"isNoCashback"`
			Faq            string `json:"faq"`
			Country        string `json:"country"`
			Region         string `json:"region"`
			Id             string `json:"id"`
			IsFavorite     bool   `json:"isFavorite"`
			IsAlert        bool   `json:"isAlert"`
			CouponsCount   int    `json:"couponsCount"`
			DealsCount     int    `json:"dealsCount"`
			CommissionRate struct {
				CashbackDesc             *string `json:"cashbackDesc"`
				IsDisableFirstOrderBonus bool    `json:"isDisableFirstOrderBonus"`
				OldRate                  string  `json:"oldRate"`
				Rate                     string  `json:"rate"`
				OriginalRate             string  `json:"originalRate"`
			} `json:"commissionRate"`
			IsDisableFirstOrderBonus bool   `json:"isDisableFirstOrderBonus"`
			IsDisableFetchingOffers  bool   `json:"isDisableFetchingOffers"`
			OldRate                  string `json:"oldRate"`
		} `json:"data"`
		PageNum    int `json:"pageNum"`
		PageSize   int `json:"pageSize"`
		TotalPages int `json:"totalPages"`
	} `json:"data"`
}

type GetShortLinksResp struct {
	Errno  int    `json:"errno"`
	Errmsg string `json:"errmsg"`
	Data   struct {
		Data string `json:"data"`
	} `json:"data"`
}
