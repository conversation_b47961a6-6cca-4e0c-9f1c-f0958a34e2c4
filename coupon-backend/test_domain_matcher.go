package main

import (
	"fmt"
	"log"
	"os"

	"coupon-backend/infra/utils"
)

func main() {
	// 设置环境变量
	os.Setenv("ENV", "live")

	// 获取域名匹配器
	domainMatcher := utils.GetDomainMatcher()

	// 测试一些域名
	testURLs := []string{
		"https://amazon.com/products",
		"https://google.com",
		"https://example.com",
		"https://microsoft.com/office",
		"https://unknown-domain.com",
		"https://netflix.com/movies",
	}

	fmt.Println("🧪 Testing Domain Matcher:")
	fmt.Println("=" * 50)

	for _, url := range testURLs {
		isFeatured := domainMatcher.IsFeaturedDomain(url)
		status := "❌ Not Featured"
		if isFeatured {
			status = "✅ Featured"
		}
		fmt.Printf("%-40s -> %s\n", url, status)
	}

	fmt.Println("\n📊 Domain Matcher Stats:")
	stats := domainMatcher.GetStats()
	fmt.Printf("Total domains loaded: %d\n", stats["total_domains"])
	fmt.Printf("Cache size: %d\n", stats["cache_size"])
	fmt.Printf("Load time: %v\n", stats["load_time"])
}
