{"permissions": {"allow": ["Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python:*)", "Bash(pip3 install:*)", "Bash(python3 -c \"\nfrom utils.template_generator import ConfigTemplateGenerator\nimport os\n\nprint(''Testing template generator...'')\ngen = ConfigTemplateGenerator()\n\n# Test data\ntest_data = {\n    ''SITE_NAME'': ''TestCoupons'',\n    ''SITE_DOMAIN'': ''testcoupons.org'',\n    ''SITE_URL'': ''https://testcoupons.org'',\n    ''SITE_TAGLINE'': ''Save More Every Day'',\n    ''CONTACT_EMAIL'': ''<EMAIL>''\n}\n\n# Generate variables.ts\nvariables_content = gen.generate_variables_ts(test_data)\nprint(''✅ Variables template generated'')\n\n# Create test output\nos.makedirs(''test_output'', exist_ok=True)\nwith open(''test_output/variables.ts'', ''w'') as f:\n    f.write(variables_content)\n    \nprint(''✅ Test file saved to test_output/variables.ts'')\n\")", "Bash(python3 -c \"\nfrom config_generator import ConfigGenerator\nimport json\n\nprint(''Testing optimized GPT requests...'')\ngenerator = ConfigGenerator()\n\n# Test with smaller requests\nprint(''\\n1. Testing branding generation (3 separate requests)...'')\nbranding = generator.generate_branding_content(''testsite.com'', ''TestSite'', ''https://api.testsite.com'')\nprint(''✅ Branding generated successfully'')\nprint(''Fields:'', list(branding.keys()) if branding else ''Failed'')\n\nprint(''\\n2. Testing site variables generation (4 separate requests)...'')\nvariables = generator.generate_site_variables(''testsite.com'', ''TestSite'', ''https://api.testsite.com'')\nprint(''✅ Variables generated successfully'')\nprint(''Fields:'', list(variables.keys()) if variables else ''Failed'')\n\nprint(''\\n3. Testing content generation (4 separate requests)...'')\ncontent = generator.generate_content_config(''testsite.com'', ''TestSite'', ''https://api.testsite.com'')\nprint(''✅ Content generated successfully'')\nprint(''Fields:'', list(content.keys()) if content else ''Failed'')\n\nprint(''\\n4. Testing footer generation (3 separate requests)...'')\nfooter = generator.generate_footer_config(''testsite.com'', ''TestSite'', ''https://api.testsite.com'')\nprint(''✅ Footer generated successfully'')\nprint(''Fields:'', list(footer.keys()) if footer else ''Failed'')\n\nprint(''\\n🎉 All optimized generators tested successfully!'')\nprint(''Each method now makes multiple small GPT requests instead of one large request.'')\n\")", "<PERSON><PERSON>(chmod:*)", "Bash(ls:*)", "Bash(cp config_generator.py config_generator_backup.py)", "<PERSON><PERSON>(mv:*)", "Bash(npm run build:config)", "Bash(npm run build:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(tsc --noEmit)", "Bash(./deploy/build.sh:*)", "Bash(go build:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(npx tsc --outDir dist-temp --target es2020 --module es2020 --moduleResolution node --skipLibCheck true src/config/theme/*.ts)", "Bash(npx tsc --noEmit)", "Bash(node scripts/generate-theme.mjs)", "<PERSON><PERSON>(tsc)", "Bash(vite build)", "Bash(pip uninstall charset-normalizer -y)", "Bash(pip install charset-normalizer --no-binary charset-normalizer)", "<PERSON><PERSON>(pip uninstall:*)", "Bash(pip install mypy --no-binary mypy)", "Bash(pip install Brotli --no-binary <PERSON>rot<PERSON>)", "<PERSON><PERSON>(pip show:*)"], "deny": []}}